'use client';

import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { signIn } from 'next-auth/react';
import { useTranslations } from 'next-intl';
import { useEffect, useState } from 'react';
import { FormProvider, useForm } from 'react-hook-form';
import { Form } from '@/components/forms/common/Form';
import { FormField } from '@/components/forms/common/FormField';
import { OTPInputField } from '@/components/forms/common/OTPInputField';
// Removed useFormSubmit import - using direct authentication handlers
import { Button } from '@/components/ui/button';
import { useMessage } from '@/hooks/useMessage';
import { zodResolver } from '@/lib/utils/zod-resolver-compat';
// Types are now imported from client validation
import {
  type ClientLoginFormValues,
  type ClientTotpFormValues,
  createClientLoginSchema,
  createClientTotpSchema,
} from '@/lib/validation/client/auth-client';

type LoginClientProps = {
  initialToast?: {
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
  } | null;
};

export function LoginClient({ initialToast }: LoginClientProps) {
  const t = useTranslations('admin');
  const tValidation = useTranslations('validation');
  const tBrand = useTranslations('brand');
  const router = useRouter();
  const { showSuccess, showError, showInfo, showDirect } = useMessage();
  const [authStep, setAuthStep] = useState<'login' | 'verify'>('login');
  const [username, setUsername] = useState('');
  const [showBackupCodeForm, setShowBackupCodeForm] = useState(false);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isVerifying, setIsVerifying] = useState(false);

  // NOTE: Removed manual setActiveSystem call - pathname-based logic in CombinedAuthProvider
  // automatically uses admin provider for /admin/* paths

  // Show initial toast message if provided
  useEffect(() => {
    if (initialToast) {
      // Use a delay to ensure the toast system is fully initialized
      setTimeout(() => {
        showDirect(initialToast.type, initialToast.message);
      }, 1000);
    }
  }, [initialToast, showDirect]);

  // Create client-side validation schemas with translations
  const loginSchema = createClientLoginSchema(tValidation);
  const totpSchema = createClientTotpSchema(tValidation);

  // Login form
  const loginForm = useForm<ClientLoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      username: '',
      password: '',
    },
  });

  // 2FA verification form
  const verifyForm = useForm<ClientTotpFormValues>({
    resolver: zodResolver(totpSchema),
    defaultValues: {
      token: '',
    },
  });

  // Handle login form submission - Direct authentication handler
  const handleLogin = async (data: ClientLoginFormValues) => {
    setIsLoggingIn(true);
    try {
      // Explicitly use the admin auth endpoint
      const result = await signIn('credentials', {
        username: data.username,
        password: data.password,
        redirect: false,
        callbackUrl: '/admin/dashboard',
      });

      if (result?.error === 'TotpRequired') {
        // Store username for 2FA verification and switch to verification step
        setUsername(data.username);
        setAuthStep('verify');
        showInfo('twoFactorRequired'); // Only show 2FA required message
        return;
      }

      if (result?.error) {
        // Handle authentication errors with centralized alert system
        showError(result.error, 'auth');
        return;
      }

      if (result?.ok) {
        // Show success message only after complete authentication
        showSuccess('loginSuccessful');
        // Add a small delay to allow the toast to show before redirecting
        setTimeout(() => {
          router.push('/admin/dashboard');
        }, 1000);
        return;
      }

      // Fallback error handling
      showError('authenticationError', 'auth');
    } catch (_error) {
      showError('errorDuringLogin', 'auth');
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Handle 2FA verification form submission - Direct authentication handler
  const handleVerification = async (data: ClientTotpFormValues) => {
    setIsVerifying(true);
    try {
      // Explicitly use the admin auth endpoint
      const result = await signIn('credentials', {
        username,
        totp: data.token,
        redirect: false,
        callbackUrl: '/admin/dashboard',
      });

      if (result?.error) {
        // Handle 2FA verification errors with centralized alert system
        showError('verificationFailed', 'auth');
        return;
      }

      if (result?.ok) {
        // Show success message only after complete authentication
        showSuccess('loginSuccessful');
        // Add a small delay to allow the toast to show before redirecting
        setTimeout(() => {
          router.push('/admin/dashboard');
        }, 1000);
        return;
      }

      // Fallback error handling
      showError('verificationFailed', 'auth');
    } catch (_error) {
      showError('verificationFailed', 'auth');
    } finally {
      setIsVerifying(false);
    }
  };

  // Handle going back to login step
  const handleBackToLogin = () => {
    setAuthStep('login');
    setUsername('');
    verifyForm.reset();
  };

  return (
    <div className="grid flex-1 items-stretch">
      {/* Admin Login Form */}
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <Link className="flex items-center gap-2 font-medium" href="/">
            <div className="flex h-6 w-6 items-center justify-center rounded-md bg-primary text-primary-foreground">
              <svg
                fill="none"
                height="16"
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                viewBox="0 0 24 24"
                width="16"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>WSCCC Logo</title>
                <path d="M3 9h18v10a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V9Z" />
                <path d="M3 9V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2v4" />
                <path d="M13 13h4" />
                <path d="M13 17h4" />
                <path d="M7 13h2v4H7z" />
              </svg>
            </div>
            {tBrand('name')}
          </Link>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-sm">
            <div className="flex flex-col gap-6 p-4 md:p-0">
              {authStep === 'login' ? (
                <>
                  <div className="flex flex-col items-center gap-2 text-center">
                    <h1 className="font-bold text-2xl">{t('adminLogin')}</h1>
                    <p className="text-balance text-muted-foreground text-sm">
                      {t('signInToAccessDashboard')}
                    </p>
                  </div>
                  <div className="grid gap-6">
                    <Form
                      className="space-y-4"
                      form={loginForm}
                      isLoading={isLoggingIn}
                      onSubmit={handleLogin}
                      submitText={t('signIn')}
                    >
                      <FormField
                        error={loginForm.formState.errors.username}
                        id="username"
                        label={t('username')}
                        placeholder={t('enterYourUsername')}
                        register={loginForm.register}
                        required
                      />
                      <FormField
                        error={loginForm.formState.errors.password}
                        id="password"
                        label={t('password')}
                        placeholder={t('enterYourPassword')}
                        register={loginForm.register}
                        required
                        type="password"
                      />
                    </Form>
                    <div className="text-center text-sm">
                      <Link
                        className="flex items-center justify-center gap-1 text-muted-foreground underline underline-offset-4 hover:text-primary"
                        href="/"
                      >
                        {t('backToHome')}
                      </Link>
                    </div>
                  </div>
                </>
              ) : (
                <>
                  <div className="flex flex-col items-center gap-2 text-center">
                    <h1 className="font-bold text-2xl">
                      {t('twoFactorAuthentication')}
                    </h1>
                    <p className="text-balance text-muted-foreground text-sm">
                      {showBackupCodeForm
                        ? t('enterBackupCode')
                        : t('enterOneTimeCode')}
                    </p>
                  </div>

                  <div className="grid gap-6">
                    {showBackupCodeForm ? (
                      <FormProvider {...verifyForm}>
                        <form
                          className="space-y-4"
                          onSubmit={verifyForm.handleSubmit(handleVerification)}
                        >
                          <OTPInputField
                            className="mb-2"
                            error={verifyForm.formState.errors.token}
                            helperText={t('enterSixCharacterBackupCode')}
                            id="token"
                            label={t('backupCode')}
                            length={6}
                            required
                          />

                          <div className="mt-2 text-center">
                            <Button
                              className="text-muted-foreground text-xs"
                              onClick={() => setShowBackupCodeForm(false)}
                              type="button"
                              variant="link"
                            >
                              {t('useAuthenticatorApp')}
                            </Button>
                          </div>

                          <Button
                            className="w-full"
                            disabled={isVerifying}
                            type="submit"
                          >
                            {isVerifying ? t('verifying') : t('verify')}
                          </Button>
                        </form>
                      </FormProvider>
                    ) : (
                      <FormProvider {...verifyForm}>
                        <form
                          className="space-y-4"
                          onSubmit={verifyForm.handleSubmit(handleVerification)}
                        >
                          <OTPInputField
                            className="mb-2"
                            error={verifyForm.formState.errors.token}
                            helperText={t('enterSixDigitCode')}
                            id="token"
                            label={t('verificationCode')}
                            length={6}
                            required
                          />

                          <div className="mt-2 text-center">
                            <Button
                              className="text-muted-foreground text-xs"
                              onClick={() => setShowBackupCodeForm(true)}
                              type="button"
                              variant="link"
                            >
                              {t('useBackupCode')}
                            </Button>
                          </div>

                          <Button
                            className="w-full"
                            disabled={isVerifying}
                            type="submit"
                          >
                            {isVerifying ? t('verifying') : t('verify')}
                          </Button>
                        </form>
                      </FormProvider>
                    )}

                    <div className="text-center text-sm">
                      <button
                        className="cursor-pointer text-muted-foreground underline underline-offset-4 hover:text-primary"
                        onClick={handleBackToLogin}
                        type="button"
                      >
                        {t('backToLogin')}
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
