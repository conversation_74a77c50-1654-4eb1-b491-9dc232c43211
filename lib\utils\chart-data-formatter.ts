/**
 * Utility functions for formatting database query results into chart data
 * and generating rich markdown responses with embedded charts
 */

export interface ChartRecommendation {
  type: string;
  reasoning: string;
  confidence: 'high' | 'medium' | 'low';
  alternatives?: string[];
}

export interface ChartData {
  type:
    | 'bar'
    | 'pie'
    | 'line'
    | 'area'
    | 'scatter'
    | 'heatmap'
    | 'radar'
    | 'funnel'
    | 'treemap'
    | 'sankey'
    | 'waffle'
    | 'table';
  title?: string;
  data: Record<string, unknown>[];
  xKey?: string;
  yKey?: string;
  nameKey?: string;
  valueKey?: string;
  // Chart recommendation metadata
  recommendation?: {
    requestedType?: string;
    suggestedType: string;
    confidence: number;
    reasoning: string;
    alternatives?: Array<{
      type: string;
      suitability: number;
      reason: string;
    }>;
    userOverride?: boolean;
  };
  // Extended properties for advanced charts
  config?: {
    // Scatter plot specific
    sizeKey?: string;
    colorKey?: string;
    // Heatmap specific
    xAxisKey?: string;
    yAxisKey?: string;
    valueKey?: string;
    // Radar chart specific
    metrics?: string[];
    // Area chart specific
    stackedKeys?: string[];
    // Treemap specific
    hierarchyKeys?: string[];
    // Sankey specific
    sourceKey?: string;
    targetKey?: string;
    // General styling
    colors?: string[];
    showLegend?: boolean;
    showGrid?: boolean;
    responsive?: boolean;
  };
}

// Regex patterns for identifying non-statistical columns (moved to top level for performance)
const NON_STATISTICAL_PATTERNS = [
  /phone/i,
  /mobile/i,
  /contact/i,
  /id$/i,
  /_id$/i,
  /code/i,
  /postal/i,
  /zip/i,
  /ssn/i,
  /abn/i,
  /acn/i,
  /number$/i,
  /num$/i,
] as const;

/**
 * Convert confidence level to numeric score
 */
function getConfidenceScore(confidence: 'high' | 'medium' | 'low'): number {
  switch (confidence) {
    case 'high':
      return 90;
    case 'medium':
      return 70;
    case 'low':
      return 50;
    default:
      return 50;
  }
}

/**
 * Simple fallback chart type when no LLM recommendation is available
 */
function getDefaultChartType(data: Record<string, unknown>[]): string {
  if (!data || data.length === 0) {
    return 'table';
  }

  const keys = Object.keys(data[0]);
  const numericKeys = keys.filter((key) => {
    const isNumeric = data.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        (typeof v === 'string' && v.trim() !== '' && !Number.isNaN(Number(v)))
      );
    });
    const isStatistical = !isNonStatisticalColumn(key);
    return isNumeric && isStatistical;
  });

  // Simple fallback: bar chart for most cases, table if no numeric data
  return numericKeys.length > 0 ? 'bar' : 'table';
}

// Helper function to identify numeric keys
function getNumericKeys(data: Record<string, unknown>[]): string[] {
  if (!data || data.length === 0) {
    return [];
  }

  const firstRow = data[0];
  const keys = Object.keys(firstRow);

  return keys.filter((key) => {
    const isNumeric = data.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        (typeof v === 'string' && v.trim() !== '' && !Number.isNaN(Number(v)))
      );
    });
    const isStatistical = !isNonStatisticalColumn(key);
    return isNumeric && isStatistical;
  });
}

// Helper function to identify categorical keys
function getCategoricalKeys(
  data: Record<string, unknown>[],
  numericKeys: string[]
): string[] {
  if (!data || data.length === 0) {
    return [];
  }

  const firstRow = data[0];
  const keys = Object.keys(firstRow);

  return keys.filter(
    (key) => !(numericKeys.includes(key) || isNonStatisticalColumn(key))
  );
}

// Helper function to create recommendation metadata
function createRecommendationMetadata(
  chartRecommendation?: ChartRecommendation
) {
  return chartRecommendation
    ? {
        suggestedType: chartRecommendation.type,
        confidence: getConfidenceScore(chartRecommendation.confidence),
        reasoning:
          chartRecommendation.reasoning || 'LLM recommended chart type',
        alternatives: (chartRecommendation.alternatives || []).map((alt) => ({
          type: alt,
          suitability: 70, // Default suitability for alternatives
          reason: 'Alternative chart type suggested by AI',
        })),
      }
    : undefined;
}

// Helper function to create scatter chart
function createScatterChart(
  data: Record<string, unknown>[],
  numericKeys: string[],
  categoricalKeys: string[],
  recommendation: ReturnType<typeof createRecommendationMetadata>
): ChartData | null {
  if (numericKeys.length < 2) {
    return null;
  }

  const [xKey, yKey] = numericKeys;
  return {
    type: 'scatter',
    data: data.map((row) => ({
      x: Number(row[xKey]),
      y: Number(row[yKey]),
      name: String(row[categoricalKeys[0]] || `Point ${data.indexOf(row) + 1}`),
    })),
    xKey: 'x',
    yKey: 'y',
    recommendation,
    config: { responsive: true },
  };
}

// Helper function to create heatmap chart
function createHeatmapChart(
  data: Record<string, unknown>[],
  keys: string[],
  numericKeys: string[],
  categoricalKeys: string[],
  recommendation: ReturnType<typeof createRecommendationMetadata>
): ChartData | null {
  if (
    !(
      keys.length === 3 &&
      numericKeys.length >= 1 &&
      categoricalKeys.length >= 2
    )
  ) {
    return null;
  }

  const valueKey = numericKeys[0];
  const [xKey, yKey] = categoricalKeys;

  return {
    type: 'heatmap',
    data: data.map((row) => ({
      x: String(row[xKey]),
      y: String(row[yKey]),
      value: Number(row[valueKey]),
    })),
    valueKey: 'value',
    recommendation,
    config: {
      xAxisKey: 'x',
      yAxisKey: 'y',
      valueKey: 'value',
      responsive: true,
      showLegend: true,
    },
  };
}

// Helper function to create area chart
function createAreaChart(
  data: Record<string, unknown>[],
  keys: string[],
  numericKeys: string[],
  recommendation: ReturnType<typeof createRecommendationMetadata>
): ChartData | null {
  const hasDateKey = keys.some(
    (key) =>
      key.toLowerCase().includes('date') ||
      key.toLowerCase().includes('time') ||
      key.toLowerCase().includes('year')
  );

  if (!hasDateKey) {
    return null;
  }

  const dateKey = keys.find(
    (key) =>
      key.toLowerCase().includes('date') ||
      key.toLowerCase().includes('time') ||
      key.toLowerCase().includes('year')
  );

  if (!dateKey || numericKeys.length < 2) {
    return null;
  }

  return {
    type: 'area',
    data: data.map((row) => ({
      date: String(row[dateKey]),
      ...numericKeys.reduce(
        (acc, key) => {
          acc[key] = Number(row[key]);
          return acc;
        },
        {} as Record<string, number>
      ),
    })),
    xKey: 'date',
    recommendation,
    config: {
      stackedKeys: numericKeys,
      showGrid: true,
      responsive: true,
    },
  };
}

// Helper function to create radar chart
function createRadarChart(
  data: Record<string, unknown>[],
  numericKeys: string[],
  categoricalKeys: string[],
  recommendation: ReturnType<typeof createRecommendationMetadata>
): ChartData | null {
  if (!(numericKeys.length >= 3 && data.length <= 10)) {
    return null;
  }

  return {
    type: 'radar',
    data: data.map((row) => ({
      name: String(row[categoricalKeys[0]] || `Item ${data.indexOf(row) + 1}`),
      ...numericKeys.reduce(
        (acc, key) => {
          acc[key] = Number(row[key]);
          return acc;
        },
        {} as Record<string, number>
      ),
    })),
    recommendation,
    config: {
      metrics: numericKeys,
      responsive: true,
      showLegend: true,
    },
  };
}

// Helper function to create treemap chart
function createTreemapChart(
  data: Record<string, unknown>[],
  numericKeys: string[],
  categoricalKeys: string[],
  recommendation: ReturnType<typeof createRecommendationMetadata>
): ChartData | null {
  if (!(categoricalKeys.length >= 2 && numericKeys.length >= 1)) {
    return null;
  }

  const valueKey = numericKeys[0];

  return {
    type: 'treemap',
    data: data.map((row) => ({
      name: String(row[categoricalKeys[0]]),
      category: String(row[categoricalKeys[1]] || 'default'),
      value: Number(row[valueKey]),
    })),
    valueKey: 'value',
    recommendation,
    config: {
      hierarchyKeys: categoricalKeys,
      valueKey: 'value',
      responsive: true,
    },
  };
}

/**
 * Analyze query results and determine the best chart type using LLM recommendation
 */
export function analyzeDataForChart(
  data: Record<string, unknown>[],
  chartRecommendation?: ChartRecommendation
): ChartData | null {
  if (!data || data.length === 0) {
    return null;
  }

  const firstRow = data[0];
  const keys = Object.keys(firstRow);
  const numericKeys = getNumericKeys(data);
  const categoricalKeys = getCategoricalKeys(data, numericKeys);

  // Use LLM recommendation or fallback to simple detection
  const chartType = chartRecommendation?.type || getDefaultChartType(data);

  // Create recommendation metadata from LLM response
  const recommendation = createRecommendationMetadata(chartRecommendation);

  // Generate chart data based on chart type (simplified)
  let chartData: ChartData | null = null;

  // Handle different chart types with simplified logic
  if (!chartData && chartType === 'scatter') {
    chartData = createScatterChart(
      data,
      numericKeys,
      categoricalKeys,
      recommendation
    );
  }

  // 2. Heatmap: 3 columns (x, y, value) or matrix-like data
  if (!chartData) {
    chartData = createHeatmapChart(
      data,
      keys,
      numericKeys,
      categoricalKeys,
      recommendation
    );
  }

  // 3. Area chart: Time series with multiple metrics
  if (!chartData) {
    chartData = createAreaChart(data, keys, numericKeys, recommendation);
  }

  // 4. Radar chart: Multiple metrics for comparison
  if (!chartData) {
    chartData = createRadarChart(
      data,
      numericKeys,
      categoricalKeys,
      recommendation
    );
  }

  // 5. Treemap: Hierarchical data with size values
  if (!chartData) {
    chartData = createTreemapChart(
      data,
      numericKeys,
      categoricalKeys,
      recommendation
    );
  }

  // Traditional charts (most common case)
  if (!chartData && keys.length === 2) {
    const [nameKey, valueKey] = keys;
    const firstValue = firstRow[valueKey];

    // Check if the value is numeric with improved detection
    if (
      typeof firstValue === 'number' ||
      (typeof firstValue === 'string' &&
        firstValue.trim() !== '' &&
        !Number.isNaN(Number(firstValue)))
    ) {
      chartData = {
        type: chartType as 'bar' | 'pie' | 'waffle',
        data: data.map((row) => ({
          name: String(row[nameKey]),
          value: Number(row[valueKey]),
        })),
        nameKey: 'name',
        valueKey: 'value',
        xKey: 'name',
        yKey: 'value',
        recommendation,
        config: {
          responsive: true,
          showLegend: ['pie', 'waffle'].includes(chartType),
        },
      };
    }
  }

  // Fallback: If no chart data generated yet, create a simple chart
  if (!chartData && numericKeys.length > 0) {
    const nameKey = categoricalKeys[0] || keys[0];
    const valueKey = numericKeys[0];

    chartData = {
      type: chartType as 'bar' | 'pie' | 'waffle' | 'line',
      data: data.map((row) => ({
        name: String(row[nameKey]),
        value: Number(row[valueKey]),
      })),
      nameKey: 'name',
      valueKey: 'value',
      xKey: 'name',
      yKey: 'value',
      recommendation,
      config: {
        responsive: true,
        showLegend: ['pie', 'waffle'].includes(chartType),
      },
    };
  }

  return chartData;
}

/**
 * Format a cell value based on its column type
 */
function formatCellValue(value: unknown, columnName: string): string {
  if (value === null || value === undefined) {
    return '';
  }

  const stringValue = String(value);

  // Handle phone numbers - keep as string, don't format as number
  if (isNonStatisticalColumn(columnName)) {
    return stringValue;
  }

  // Handle regular numeric values
  if (
    typeof value === 'number' ||
    (!Number.isNaN(Number(stringValue)) && stringValue.trim() !== '')
  ) {
    const numValue = Number(stringValue);
    // Only format as number if it's not a phone/ID field and is a reasonable number
    if (!isNonStatisticalColumn(columnName) && numValue < 1_000_000_000) {
      return numValue.toLocaleString();
    }
  }

  return stringValue;
}

/**
 * Format query results into a markdown table
 */
export function formatAsMarkdownTable(data: Record<string, unknown>[]): string {
  if (!data || data.length === 0) {
    return 'No data available.';
  }

  const keys = Object.keys(data[0]);

  // Create header
  const header = `| ${keys.join(' | ')} |`;
  const separator = `| ${keys.map(() => '---').join(' | ')} |`;

  // Create rows with proper formatting and escape markdown special characters
  const rows = data.map(
    (row) =>
      `| ${keys
        .map((key) => {
          const cellValue = formatCellValue(row[key], key);
          // Escape pipes, newlines, and backticks for robust markdown rendering
          return cellValue
            .replaceAll('|', '\\|')
            .replaceAll('\n', ' ')
            .replaceAll('\r', ' ')
            .replaceAll('`', '\\`');
        })
        .join(' | ')} |`
  );

  return [header, separator, ...rows].join('\n');
}

/**
 * Generate rich markdown response with embedded charts and tables
 */
export function generateRichResponse(
  aiResponse: string,
  queryResult: Record<string, unknown>[] | null,
  queryType?: string,
  chartRecommendation?: ChartRecommendation
): string {
  if (!queryResult || queryResult.length === 0) {
    return aiResponse;
  }

  let richResponse = aiResponse;

  // Analyze data for potential charts using LLM recommendation
  const chartData = analyzeDataForChart(queryResult, chartRecommendation);

  if (chartData && queryResult.length > 1) {
    // Add chart
    const chartTitle = generateChartTitle(queryResult, queryType);
    chartData.title = chartTitle;

    richResponse += '\n\n## 📊 Data Visualization\n\n';
    richResponse += `CHART_DATA:${JSON.stringify(chartData)}\n\n`;
  }

  // Add formatted table with export data
  richResponse += '\n\n## 📋 Detailed Results\n\n';

  // Embed export data for the table renderer to use
  // Convert BigInt values to Numbers for JSON serialization
  const serializedData = queryResult.map((row) => {
    const serializedRow: Record<string, unknown> = {};
    for (const [key, value] of Object.entries(row)) {
      if (typeof value === 'bigint') {
        serializedRow[key] = Number(value);
      } else {
        serializedRow[key] = value;
      }
    }
    return serializedRow;
  });

  const exportMetadata = {
    data: serializedData,
    title: generateChartTitle(serializedData, queryType),
    queryType: queryType || 'general',
    totalRecords: serializedData.length,
  };
  richResponse += `TABLE_EXPORT_DATA:${JSON.stringify(exportMetadata)}\n\n`;

  if (queryResult.length <= 20) {
    // For small datasets, show full table
    richResponse += formatAsMarkdownTable(queryResult);
  } else {
    // For large datasets, show summary + sample
    richResponse += `**Total Records:** ${queryResult.length}\n\n`;
    richResponse += '**Sample Data (first 10 records):**\n\n';
    richResponse += formatAsMarkdownTable(queryResult.slice(0, 10));
    richResponse += `\n\n*... and ${queryResult.length - 10} more records*`;
  }

  // Add SQL query if available (this would be passed from the API)
  // richResponse += `\n\n## 🔍 Query Details\n\n`;
  // richResponse += '```sql\n' + sqlQuery + '\n```';

  return richResponse;
}

/**
 * Generate appropriate chart title based on data and query type
 */
function generateChartTitle(
  data: Record<string, unknown>[],
  queryType?: string
): string {
  const keys = Object.keys(data[0]);

  // Try to infer from column names
  if (keys.some((key) => key.toLowerCase().includes('suburb'))) {
    return 'Distribution by Suburb';
  }

  if (
    keys.some(
      (key) =>
        key.toLowerCase().includes('gender') ||
        key.toLowerCase().includes('sex')
    )
  ) {
    return 'Gender Distribution';
  }

  if (keys.some((key) => key.toLowerCase().includes('age'))) {
    return 'Age Distribution';
  }

  if (keys.some((key) => key.toLowerCase().includes('year'))) {
    return 'Yearly Trends';
  }

  if (keys.some((key) => key.toLowerCase().includes('month'))) {
    return 'Monthly Distribution';
  }

  // Fallback based on query type
  switch (queryType) {
    case 'members':
      return 'Member Statistics';
    case 'households':
      return 'Household Statistics';
    case 'unique_codes':
      return 'Unique Code Statistics';
    default:
      return 'Data Distribution';
  }
}

/**
 * Detect if query results contain numerical data suitable for charts
 */
export function hasNumericalData(data: Record<string, unknown>[]): boolean {
  if (!data || data.length === 0) {
    return false;
  }

  const firstRow = data[0];
  return Object.values(firstRow).some((value) => {
    return (
      typeof value === 'number' ||
      (typeof value === 'string' &&
        value.trim() !== '' &&
        !Number.isNaN(Number(value)))
    );
  });
}

/**
 * Format large numbers for display
 */
export function formatNumber(num: number): string {
  if (num >= 1_000_000) {
    return `${(num / 1_000_000).toFixed(1)}M`;
  }
  if (num >= 1000) {
    return `${(num / 1000).toFixed(1)}K`;
  }
  return num.toString();
}

/**
 * Check if a column should be excluded from statistical analysis
 */
function isNonStatisticalColumn(columnName: string): boolean {
  return NON_STATISTICAL_PATTERNS.some((pattern) => pattern.test(columnName));
}

/**
 * Generate summary statistics for numerical columns (excluding IDs, phone numbers, etc.)
 */
export function generateSummaryStats(data: Record<string, unknown>[]): string {
  if (!data || data.length === 0) {
    return '';
  }

  const numericalColumns = Object.keys(data[0]).filter((key) => {
    // Check if column is numeric with improved detection
    const isNumeric = data.every((row) => {
      const v = row[key];
      return (
        typeof v === 'number' ||
        (typeof v === 'string' && v.trim() !== '' && !Number.isNaN(Number(v)))
      );
    });

    // Exclude non-statistical columns (phone numbers, IDs, etc.)
    const isStatistical = !isNonStatisticalColumn(key);

    return isNumeric && isStatistical;
  });

  if (numericalColumns.length === 0) {
    return '';
  }

  let summary = '\n\n## 📈 Summary Statistics\n\n';

  for (const column of numericalColumns) {
    const values = data
      .map((row) => Number(row[column]))
      .filter((val) => !Number.isNaN(val));
    if (values.length === 0) {
      continue;
    }

    const sum = values.reduce((a, b) => a + b, 0);
    const avg = sum / values.length;
    const min = Math.min(...values);
    const max = Math.max(...values);

    summary += `**${column}:**\n`;
    summary += `- Total: ${formatNumber(sum)}\n`;
    summary += `- Average: ${formatNumber(Math.round(avg * 100) / 100)}\n`;
    summary += `- Range: ${formatNumber(min)} - ${formatNumber(max)}\n\n`;
  }

  return summary;
}
