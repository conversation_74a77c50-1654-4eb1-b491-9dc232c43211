'use client';

import { Loader2, Search, SlidersHorizontal, X } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { useCallback, useEffect, useRef, useState } from 'react';
import { EditMemberDialog } from '@/components/admin/members/edit-member-dialog';
import { MembersTable } from '@/components/admin/members/members-table';
import { ViewMemberDialog } from '@/components/admin/members/view-member-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { useMessage } from '@/hooks/useMessage';
import { useSearch } from '@/hooks/useSearch';
import type { ICensusYear, IMemberWithDetails } from '@/types';

interface MembersClientProps {
  censusYears: ICensusYear[];
  initialMembers: unknown[];
  initialTotal: number;
  initialPageSize: number;
}

interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
}

// Helper function to build query parameters for members
function buildMembersQueryParams(
  searchTerm: string | null,
  filterGender: string | null,
  filterRelationship: string | null,
  filterCensusYearId: number | null,
  filterSacramentStatus: string | null,
  pagination: { page: number; pageSize: number },
  sortBy: string,
  sortOrder: string
): URLSearchParams {
  const params = new URLSearchParams();
  if (searchTerm) {
    params.append('searchTerm', searchTerm);
  }
  if (filterGender) {
    params.append('gender', filterGender);
  }
  if (filterRelationship) {
    params.append('relationship', filterRelationship);
  }
  if (filterCensusYearId !== null) {
    params.append('censusYearId', filterCensusYearId.toString());
  }
  if (filterSacramentStatus) {
    params.append('sacramentStatus', filterSacramentStatus);
  }
  params.append('page', pagination.page.toString());
  params.append('pageSize', pagination.pageSize.toString());
  params.append('sortBy', sortBy);
  params.append('sortOrder', sortOrder);
  return params;
}

// Helper function to fetch members data
async function fetchMembersData(params: URLSearchParams) {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), 15_000);

  try {
    const response = await fetch(
      `/api/admin/members/search?${params.toString()}`,
      {
        signal: controller.signal,
        headers: {
          'Cache-Control': 'no-cache, no-store, must-revalidate',
          Pragma: 'no-cache',
        },
      }
    );

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorData = await response
        .json()
        .catch(() => ({ error: `Server error: ${response.status}` }));
      throw new Error(
        errorData.error || `Failed to fetch members: ${response.statusText}`
      );
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

export function MembersClient({
  censusYears,
  initialMembers,
  initialTotal,
  initialPageSize,
}: MembersClientProps) {
  const { showSuccess, showError, showDirect, showAlert } = useMessage();
  const t = useTranslations('admin');
  const tCommon = useTranslations('common');
  const tForms = useTranslations('forms');

  // Helper function for sacrament status display
  const getSacramentStatusText = useCallback(
    (status: string) => {
      switch (status) {
        case 'none':
          return tCommon('noSacraments');
        case 'partial':
          return tCommon('someSacraments');
        case 'complete':
          return tCommon('allSacraments');
        default:
          return status;
      }
    },
    [tCommon]
  );
  const tGenders = useTranslations('genders');
  const tRelationships = useTranslations('relationships');
  const tNotifications = useTranslations('notifications');

  // State for member data and UI
  const [members, setMembers] = useState<IMemberWithDetails[]>(
    initialMembers as IMemberWithDetails[]
  );
  const [loading, setLoading] = useState(false); // Start with false since we have initial data
  const [pagination, setPagination] = useState<PaginationState>({
    page: 1,
    pageSize: initialPageSize,
    total: initialTotal,
    totalPages: Math.ceil(initialTotal / initialPageSize),
  });

  // Sorting and filtering state
  const [sortBy, setSortBy] = useState('memberId');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterGender, setFilterGender] = useState<string | null>(null);
  const [filterRelationship, setFilterRelationship] = useState<string | null>(
    null
  );
  const [filterCensusYearId, setFilterCensusYearId] = useState<number | null>(
    null
  );
  const [filterSacramentStatus, setFilterSacramentStatus] = useState<
    string | null
  >(null);

  // Dialog state
  const [selectedMember, setSelectedMember] =
    useState<IMemberWithDetails | null>(null);
  const [isViewDialogOpen, setIsViewDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);

  // Search functionality
  const searchInputRef = useRef<HTMLInputElement>(null);
  const {
    searchTerm,
    debouncedSearchTerm,
    isSearching,
    setSearchTerm,
    clearSearch,
  } = useSearch({
    debounceDelay: 300,
  });

  // Get active census year
  const activeCensusYear = censusYears.find((year) => year.isActive) || null;

  // Fetch members with pagination and filtering
  const fetchMembers = useCallback(async () => {
    try {
      setLoading(true);

      // Build query parameters using helper function
      const params = buildMembersQueryParams(
        debouncedSearchTerm,
        filterGender,
        filterRelationship,
        filterCensusYearId,
        filterSacramentStatus,
        pagination,
        sortBy,
        sortOrder
      );

      // Fetch members data using helper function
      const data = await fetchMembersData(params);

      // Validate response data structure
      if (!(data.members && Array.isArray(data.members) && data.pagination)) {
        throw new Error('Invalid response format from server');
      }

      setMembers(data.members);
      setPagination(data.pagination);
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'An unexpected error occurred while fetching members';

      showDirect('error', errorMessage);

      // Set empty state on error to prevent showing stale data
      setMembers([]);
      setPagination((prev) => ({ ...prev, total: 0, totalPages: 0 }));
    } finally {
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
}, [
    debouncedSearchTerm,
    filterGender,
    filterRelationship,
    filterCensusYearId,
    filterSacramentStatus,
    pagination.page,
    pagination.pageSize,
    sortBy,
    sortOrder,
    showDirect,
    // Note: We intentionally use pagination.page/pageSize instead of pagination object
  ])

// Initial fetch
useEffect(() => {
  fetchMembers();
}, [fetchMembers]);

// Handle page change
const handlePageChange = (newPage: number) => {
  setPagination((prev) => ({ ...prev, page: newPage }));
};

// Handle page size change
const handlePageSizeChange = (newPageSize: number) => {
  setPagination((prev) => ({ ...prev, page: 1, pageSize: newPageSize }));
};

// Handle sorting change
const handleSortChange = (column: string) => {
  if (sortBy === column) {
    setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
  } else {
    setSortBy(column);
    setSortOrder('asc');
  }
  setPagination((prev) => ({ ...prev, page: 1 }));
};

// Handle search input change
const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  setSearchTerm(e.target.value);
  // Reset to first page when search changes
  setPagination((prev) => ({ ...prev, page: 1 }));
};

// Handle filter changes
const handleGenderFilterChange = (value: string) => {
  setFilterGender(value === 'all' ? null : value);
  setPagination((prev) => ({ ...prev, page: 1 }));
};

const handleRelationshipFilterChange = (value: string) => {
  setFilterRelationship(value === 'all' ? null : value);
  setPagination((prev) => ({ ...prev, page: 1 }));
};

const handleCensusYearFilterChange = (value: string) => {
  setFilterCensusYearId(value === 'all' ? null : Number.parseInt(value, 10));
  setPagination((prev) => ({ ...prev, page: 1 }));
};

const handleSacramentStatusFilterChange = (value: string) => {
  setFilterSacramentStatus(value === 'all' ? null : value);
  setPagination((prev) => ({ ...prev, page: 1 }));
};

// Clear all filters
const clearAllFilters = () => {
  setFilterGender(null);
  setFilterRelationship(null);
  setFilterCensusYearId(null);
  setFilterSacramentStatus(null);
  clearSearch();
  setPagination((prev) => ({ ...prev, page: 1 }));
};

// Utility function to reset body styles and ensure interactivity
const resetBodyStyles = useCallback(() => {
  // Force pointer-events to be enabled
  document.body.style.pointerEvents = 'auto';
  // Remove any overflow hidden that might have been added
  document.body.style.overflow = '';
  // Remove any padding right that might have been added to compensate for scrollbar
  document.body.style.paddingRight = '';
}, []);

// Handle member actions
const handleViewMember = (member: IMemberWithDetails) => {
  setSelectedMember(member);
  setIsViewDialogOpen(true);
};

const handleEditMember = (member: IMemberWithDetails) => {
  setSelectedMember(member);
  setIsEditDialogOpen(true);
};

const handleMemberUpdated = () => {
  fetchMembers();
  setIsEditDialogOpen(false);
  setSelectedMember(null);

  // Force body to be interactive
  resetBodyStyles();

  showSuccess('memberUpdated');
};

const handleMemberDeleted = () => {
  fetchMembers();
  setIsViewDialogOpen(false);
  setSelectedMember(null);

  // Force body to be interactive
  resetBodyStyles();

  showSuccess('memberDeleted');
};

// Track ongoing delete operations to prevent duplicates
const [isDeletingMembers, setIsDeletingMembers] = useState(false);

// Handle bulk delete of members
const handleDeleteSelected = async (memberIds: number[]) => {
  // Prevent duplicate requests
  if (isDeletingMembers) {
    return;
  }

  try {
    setIsDeletingMembers(true);

    const response = await fetch('/api/admin/members/bulk-delete', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ memberIds }),
    });

    const data = await response.json();

    if (!response.ok) {
      throw new Error(data.error || data.message || 'Failed to delete members');
    }

    // Refresh the members list
    fetchMembers();

    // Handle different response scenarios with proper next-intl parameter passing
    if (data.summary?.failed > 0 && data.summary?.successful > 0) {
      // Partial success - use proper next-intl parameter passing (TypeScript definitions now fixed)
      const message = tNotifications('bulkDeleteMembersPartialSuccess', {
        deletedCount: data.summary.successful,
        skippedCount: data.summary.failed,
      });
      showAlert('success', message);
    } else if (data.summary?.failed > 0) {
      // All failed
      showError('BulkDeleteFailed');
    } else {
      // All successful - use proper next-intl parameter passing (TypeScript definitions now fixed)
      const message = tNotifications('bulkDeleteMembersSuccess', {
        count: data.summary.successful,
      });
      showAlert('success', message);
    }
  } catch (_error) {
    showError('BulkDeleteFailed');
  } finally {
    setIsDeletingMembers(false);
  }
};

// Handle edit dialogue open/close
const handleEditDialogOpenChange = useCallback((open: boolean) => {
  setIsEditDialogOpen(open);
  if (!open) {
    // Reset selectedMember when dialogue is closed
    setSelectedMember(null);

    // Force body to be interactive
    document.body.style.pointerEvents = 'auto';
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }
}, []);

// Handle view dialogue open/close
const handleViewDialogOpenChange = useCallback((open: boolean) => {
  setIsViewDialogOpen(open);
  if (!open) {
    // Reset selectedMember when dialogue is closed
    setSelectedMember(null);

    // Force body to be interactive
    document.body.style.pointerEvents = 'auto';
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }
}, []);

// Ensure body is interactive when dialogues are closed
useEffect(() => {
  // If dialogues are closed, reset body styles
  if (!(isViewDialogOpen || isEditDialogOpen)) {
    // Force pointer-events to be enabled
    document.body.style.pointerEvents = 'auto';
    // Remove any overflow hidden that might have been added
    document.body.style.overflow = '';
    // Remove any padding right that might have been added to compensate for scrollbar
    document.body.style.paddingRight = '';
  }

  // Cleanup function to ensure proper cleanup when component unmounts
  return () => {
    // Force pointer-events to be enabled
    document.body.style.pointerEvents = 'auto';
    // Remove any overflow hidden that might have been added
    document.body.style.overflow = '';
    // Remove any padding right that might have been added to compensate for scrollbar
    document.body.style.paddingRight = '';
  };
}, [isViewDialogOpen, isEditDialogOpen]);

// Handle keyboard shortcut for search
useEffect(() => {
  const handleKeyDown = (e: KeyboardEvent) => {
    // Focus search input when "/" is pressed
    if (e.key === '/' && document.activeElement !== searchInputRef.current) {
      e.preventDefault();
      searchInputRef.current?.focus();
    }
    // Close dialogues when Escape is pressed
    if (e.key === 'Escape') {
      if (isViewDialogOpen) {
        setIsViewDialogOpen(false);
        setSelectedMember(null);
        // Force body to be interactive
        document.body.style.pointerEvents = 'auto';
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
      }
      if (isEditDialogOpen) {
        setIsEditDialogOpen(false);
        setSelectedMember(null);
        // Force body to be interactive
        document.body.style.pointerEvents = 'auto';
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
      }
    }
  };

  document.addEventListener('keydown', handleKeyDown);
  return () => document.removeEventListener('keydown', handleKeyDown);
}, [isViewDialogOpen, isEditDialogOpen]);

return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col items-start justify-between gap-4 sm:flex-row sm:items-center">
        <div>
          <h1 className="font-bold text-2xl">{t('membersManagement')}</h1>
          <p className="text-muted-foreground">
            {t('membersManagementSubtitle')}
          </p>
        </div>
      </div>

      {/* Search and filter section */}
      <div className="rounded-lg border border-border/50 bg-background/50 backdrop-blur-sm">
        <div className="p-6 pb-3">
          <h3 className="font-semibold text-lg tracking-tight">
            {t('searchAndFilter')}
          </h3>
          <p className="text-muted-foreground text-sm">
            {t('findSpecificMembers')}
          </p>
        </div>
        <div className="px-6 pb-6">
          <div className="space-y-4">
            {/* Search bar with integrated filter button */}
            <div className="flex gap-2">
              <div className="relative flex-1">
                <Search
                  aria-hidden="true"
                  className="absolute top-3 left-0 h-4 w-4 text-muted-foreground"
                />
                <Input
                  aria-label={tCommon('searchMembers')}
                  autoComplete="off"
                  className="pr-20 pl-6"
                  id="member-search"
                  name="member-search"
                  onChange={handleSearchChange}
                  placeholder={tForms('pressToFocus')}
                  ref={searchInputRef}
                  type="search"
                  value={searchTerm}
                  variant="line"
                />
                <span className="sr-only" id="search-description">
                  Search for members by name, mobile, suburb, or hobby
                </span>
                {isSearching && (
                  <Loader2
                    aria-hidden="true"
                    className="absolute top-3 right-12 h-4 w-4 animate-spin text-muted-foreground"
                  />
                )}
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      aria-haspopup="dialog"
                      aria-label={tCommon('openFilterOptions')}
                      className="absolute top-1 right-1 h-7 cursor-pointer gap-1 px-2 text-muted-foreground hover:text-foreground"
                      id="filter-button"
                      size="sm"
                      variant="ghost"
                    >
                      <SlidersHorizontal
                        aria-hidden="true"
                        className="h-4 w-4"
                      />
                      <span className="sr-only sm:not-sr-only sm:whitespace-nowrap">
                        {tCommon('filters')}
                      </span>
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent
                    align="end"
                    aria-labelledby="filter-heading"
                    className="w-[280px] p-4"
                    role="dialog"
                    sideOffset={8}
                  >
                    <div className="grid gap-4">
                      <div className="space-y-2">
                        <h4
                          className="font-medium leading-none"
                          id="filter-heading"
                        >
                          {tCommon('genderFilter')}
                        </h4>
                        <Select
                          aria-label={tCommon('filterByGender')}
                          name="gender-filter"
                          onValueChange={handleGenderFilterChange}
                          value={filterGender === null ? 'all' : filterGender}
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue placeholder={tForms('selectGender')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {tCommon('allGenders')}
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="male">
                              {tGenders('male')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="female"
                            >
                              {tGenders('female')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="other"
                            >
                              {tGenders('other')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <h4 className="font-medium leading-none">
                          {tCommon('relationshipFilter')}
                        </h4>
                        <Select
                          aria-label={tCommon('filterByRelationship')}
                          name="relationship-filter"
                          onValueChange={handleRelationshipFilterChange}
                          value={
                            filterRelationship === null
                              ? 'all'
                              : filterRelationship
                          }
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue
                              placeholder={tForms('selectRelationship')}
                            />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {tCommon('allRelationships')}
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="head">
                              {tRelationships('head')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="spouse"
                            >
                              {tRelationships('spouse')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="child"
                            >
                              {tRelationships('child')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="parent"
                            >
                              {tRelationships('parent')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="relative"
                            >
                              {tRelationships('relative')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="other"
                            >
                              {tRelationships('other')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <h4 className="font-medium leading-none">
                          {t('censusYear')}
                        </h4>
                        <Select
                          aria-label={tCommon('filterByCensusYear')}
                          name="census-year-filter"
                          onValueChange={handleCensusYearFilterChange}
                          value={
                            filterCensusYearId === null
                              ? 'all'
                              : filterCensusYearId.toString()
                          }
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue placeholder={t('selectYear')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {tCommon('allYears')}
                            </SelectItem>
                            {censusYears
                              .sort((a, b) => b.year - a.year)
                              .map((year) => (
                                <SelectItem
                                  className="cursor-pointer"
                                  key={year.id}
                                  value={year.id.toString()}
                                >
                                  {year.year}{' '}
                                  {year.isActive
                                    ? t('activeStatus')
                                    : t('archivedStatus')}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <Separator />

                      <div className="space-y-2">
                        <h4 className="font-medium leading-none">
                          {tCommon('sacramentStatus')}
                        </h4>
                        <Select
                          aria-label={tCommon('filterBySacramentStatus')}
                          name="sacrament-status-filter"
                          onValueChange={handleSacramentStatusFilterChange}
                          value={
                            filterSacramentStatus === null
                              ? 'all'
                              : filterSacramentStatus
                          }
                        >
                          <SelectTrigger className="cursor-pointer">
                            <SelectValue placeholder={tForms('selectStatus')} />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem className="cursor-pointer" value="all">
                              {tCommon('allStatuses')}
                            </SelectItem>
                            <SelectItem className="cursor-pointer" value="none">
                              {tCommon('noSacraments')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="partial"
                            >
                              {tCommon('someSacraments')}
                            </SelectItem>
                            <SelectItem
                              className="cursor-pointer"
                              value="complete"
                            >
                              {tCommon('allSacraments')}
                            </SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <Button
                        className="mt-2"
                        onClick={clearAllFilters}
                        size="sm"
                        variant="outline"
                      >
                        {tCommon('resetFilters')}
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            {/* Active filters display */}
            <div className="flex flex-wrap gap-2">
              {(searchTerm ||
                filterGender ||
                filterRelationship ||
                filterCensusYearId !== null ||
                filterSacramentStatus) && (
                <div className="flex flex-wrap items-center gap-2">
                  {searchTerm && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {tCommon('search')}: {searchTerm}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={clearSearch}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove search filter</span>
                      </Button>
                    </Badge>
                  )}
                  {filterGender && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {tCommon('gender')}:{' '}
                        {tGenders(filterGender as 'male' | 'female' | 'other')}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleGenderFilterChange('all')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">Remove gender filter</span>
                      </Button>
                    </Badge>
                  )}
                  {filterRelationship && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {tCommon('relationship')}:{' '}
                        {tRelationships(
                          filterRelationship as
                            | 'head'
                            | 'spouse'
                            | 'child'
                            | 'parent'
                            | 'relative'
                            | 'other'
                        )}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleRelationshipFilterChange('all')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          Remove relationship filter
                        </span>
                      </Button>
                    </Badge>
                  )}
                  {filterCensusYearId !== null && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        Year:{' '}
                        {
                          censusYears.find((y) => y.id === filterCensusYearId)
                            ?.year
                        }
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleCensusYearFilterChange('all')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          Remove census year filter
                        </span>
                      </Button>
                    </Badge>
                  )}
                  {filterSacramentStatus && (
                    <Badge
                      className="flex h-7 items-center gap-1 px-3"
                      variant="secondary"
                    >
                      <span>
                        {t('sacraments')}:{' '}
                        {getSacramentStatusText(filterSacramentStatus)}
                      </span>
                      <Button
                        className="-mr-1 ml-1 h-4 w-4 rounded-full"
                        onClick={() => handleSacramentStatusFilterChange('all')}
                        size="icon"
                        variant="ghost"
                      >
                        <X className="h-3 w-3" />
                        <span className="sr-only">
                          {t('removeSacramentStatusFilter')}
                        </span>
                      </Button>
                    </Badge>
                  )}

                  {(searchTerm ||
                    filterGender ||
                    filterRelationship ||
                    filterCensusYearId !== null ||
                    filterSacramentStatus) && (
                    <Button
                      className="h-7 px-2 text-muted-foreground"
                      onClick={clearAllFilters}
                      size="sm"
                      variant="ghost"
                    >
                      {tCommon('clear')} {tCommon('selectAll').toLowerCase()}
                    </Button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Members table */}
      <MembersTable
        activeCensusYear={activeCensusYear}
        isDeletingMembers={isDeletingMembers}
        loading={loading}
        members={members}
        onDeleteSelected={handleDeleteSelected}
        onEdit={handleEditMember}
        onPageChange={handlePageChange}
        onPageSizeChange={handlePageSizeChange}
        onSortChange={handleSortChange}
        onView={handleViewMember}
        pagination={pagination}
        searchTerm={searchTerm}
        sortBy={sortBy}
        sortOrder={sortOrder}
      />

      {/* View member dialogue */}
      {selectedMember && (
        <ViewMemberDialog
          memberId={selectedMember.memberId}
          onMemberDeleted={handleMemberDeleted}
          onOpenChange={handleViewDialogOpenChange}
          open={isViewDialogOpen}
        />
      )}

      {/* Edit member dialogue */}
      {selectedMember && (
        <EditMemberDialog
          censusYears={censusYears}
          member={selectedMember}
          onMemberUpdated={handleMemberUpdated}
          onOpenChange={handleEditDialogOpenChange}
          open={isEditDialogOpen}
        />
      )}
    </div>
  );
}
