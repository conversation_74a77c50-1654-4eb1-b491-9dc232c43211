import { FlatCompat } from '@eslint/eslintrc';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const compat = new FlatCompat({
  baseDirectory: __dirname,
});

const config = [
  // Ignore patterns
  {
    ignores: [
      '.next/**',
      'node_modules/**',
      'out/**',
      'build/**',
      'dist/**',
      'coverage/**',
      'public/**',
      '*.generated.*',
      'new_server/**',
    ],
  },

  // Use Next.js ESLint configuration
  ...compat.extends('next/core-web-vitals'),

  // Additional rules
  {
    rules: {
      'react/react-in-jsx-scope': 'off',
    },
  },
];

export default config;
