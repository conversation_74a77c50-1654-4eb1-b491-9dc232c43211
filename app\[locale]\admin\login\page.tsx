import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth/auth-options';
import { LoginClient } from './login-client';
import { ToastHandler } from './toast-handler';
import { AdminUrlParamHandler } from './url-param-handler';

// Force dynamic rendering to prevent DYNAMIC_SERVER_USAGE errors
export const dynamic = 'force-dynamic';

export default async function AdminLoginPage() {
  // Check if user is already authenticated
  const session = await getServerSession(authOptions);

  // If user is authenticated, redirect to dashboard
  if (session) {
    redirect('/admin/dashboard');
  }

  // Read the toast cookie
  const cookieStore = await cookies();
  const toastCookie = cookieStore.get('auth_toast');

  let initialToast: {
    type: 'success' | 'error' | 'info' | 'warning';
    message: string;
  } | null = null;

  if (toastCookie) {
    try {
      initialToast = JSON.parse(toastCookie.value);
      // Note: Cookie deletion will be handled by the client-side toast handler
      // using a Server Action (cookies can only be modified in Server Actions or Route Handlers)
    } catch (_error) {
      // Silently ignore cookie parsing errors
    }
  }

  return (
    <>
      <ToastHandler toast={initialToast} />
      <AdminUrlParamHandler />
      <LoginClient initialToast={initialToast} />
    </>
  );
}
