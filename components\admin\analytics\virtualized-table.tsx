'use client';

import {
  Ch<PERSON><PERSON><PERSON>ef<PERSON>,
  ChevronRight,
  MoreHorizontal,
  Table as TableIcon,
} from 'lucide-react';
import { useTranslations } from 'next-intl';
import React, { useCallback, useMemo, useState } from 'react';
import { FixedSizeList as List } from 'react-window';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';

// Configuration for table virtualization thresholds
// These should match the TABLE_CONFIG in rich-message-renderer.tsx
const TABLE_VIRTUALIZATION_CONFIG = {
  LARGE_TABLE_THRESHOLD: 100,
  PAGINATION_THRESHOLD: 25, // Match TABLE_CONFIG.PAGINATION_THRESHOLD
  ITEMS_PER_PAGE: 20, // Match TABLE_CONFIG.ITEMS_PER_PAGE
  VIRTUAL_ROW_HEIGHT: 45,
  VIRTUAL_CONTAINER_HEIGHT: 500,
  HEADER_HEIGHT: 45,
};

interface VirtualizedTableProps {
  children: React.ReactNode;
  enableVirtualization?: boolean;
  itemsPerPage?: number;
  className?: string;
}

// Helper function to safely get children from props
function getChildrenFromProps(element: React.ReactElement): React.ReactNode {
  return (element.props as { children?: React.ReactNode }).children;
}

// Helper function to extract headers from thead element
function extractHeaders(child: React.ReactElement): React.ReactNode[] {
  const theadChildren = React.Children.toArray(getChildrenFromProps(child));
  for (const headerRow of theadChildren) {
    if (React.isValidElement(headerRow) && headerRow.type === 'tr') {
      return React.Children.toArray(getChildrenFromProps(headerRow));
    }
  }
  return [];
}

// Helper function to extract rows from tbody element
function extractRows(child: React.ReactElement): React.ReactNode[] {
  const tbodyChildren = React.Children.toArray(getChildrenFromProps(child));
  return tbodyChildren.filter(
    (row) => React.isValidElement(row) && row.type === 'tr'
  );
}

// Extract table data from React children
function extractTableData(children: React.ReactNode) {
  const childrenArray = React.Children.toArray(children);
  let headers: React.ReactNode[] = [];
  let rows: React.ReactNode[] = [];

  for (const child of childrenArray) {
    if (React.isValidElement(child)) {
      if (child.type === 'thead') {
        headers = extractHeaders(child);
      } else if (child.type === 'tbody') {
        rows = extractRows(child);
      }
    }
  }

  return { headers, rows };
}

// TableChunk component for virtualized table
const TableChunk = React.memo(
  ({
    index,
    style,
    chunkedRows,
    headers,
    rows
  }: {
    index: number;
    style: React.CSSProperties;
    chunkedRows: React.ReactNode[][];
    headers: React.ReactNode[];
    rows: React.ReactNode[];
  }) => {
    const chunkRows = chunkedRows[index];

    return (
      <div className="px-4" style={style}>
        <div className="mb-4 overflow-hidden rounded-lg border border-slate-200 bg-white dark:border-slate-700 dark:bg-slate-900">
          <div className="border-slate-200 border-b bg-slate-50 px-3 py-2 dark:border-slate-700 dark:bg-slate-800">
            <div className="flex items-center gap-2">
              <TableIcon className="h-4 w-4 text-slate-500" />
              <span className="font-medium text-slate-700 text-sm dark:text-slate-300">
                Rows {index * TABLE_VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE + 1}{' '}
                -{' '}
                {Math.min(
                  (index + 1) * TABLE_VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE,
                  rows.length
                )}
              </span>
              <Badge className="text-xs" variant="outline">
                {chunkRows.length} rows
              </Badge>
            </div>
          </div>
          <div className="thin-scrollbar w-full max-w-full overflow-x-auto">
            <table
              className="analytics-table w-full"
            >
              {headers.length > 0 && (
                <thead>
                  <tr>{headers}</tr>
                </thead>
              )}
              <tbody>{chunkRows}</tbody>
            </table>
          </div>
        </div>
      </div>
    );
  }
);
TableChunk.displayName = 'TableChunk';

// Paginated table component for medium datasets
function PaginatedTable({
  children,
  className,
  itemsPerPage = TABLE_VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE,
}: VirtualizedTableProps) {
  const t = useTranslations('common');
  const _tPagination = useTranslations('pagination');
  const [currentPage, setCurrentPage] = useState(0);
  const { headers, rows } = extractTableData(children);

  const totalPages = Math.ceil(rows.length / itemsPerPage);
  const startIndex = currentPage * itemsPerPage;
  const endIndex = Math.min(startIndex + itemsPerPage, rows.length);
  const paginatedRows = rows.slice(startIndex, endIndex);

  const handlePrevPage = useCallback(() => {
    setCurrentPage((prev) => Math.max(0, prev - 1));
  }, []);

  const handleNextPage = useCallback(() => {
    setCurrentPage((prev) => Math.min(totalPages - 1, prev + 1));
  }, [totalPages]);

  const handlePageClick = useCallback((page: number) => {
    setCurrentPage(page);
  }, []);

  // Generate page numbers for pagination
  const getVisiblePages = () => {
    const maxVisible = 5;
    const half = Math.floor(maxVisible / 2);
    let start = Math.max(0, currentPage - half);
    const end = Math.min(totalPages - 1, start + maxVisible - 1);

    if (end - start < maxVisible - 1) {
      start = Math.max(0, end - maxVisible + 1);
    }

    return Array.from({ length: end - start + 1 }, (_, i) => start + i);
  };

  return (
    <div className={`w-full ${className || ''}`}>
      {/* Table */}
      <div className="thin-scrollbar analytics-table-container w-full max-w-full overflow-x-auto">
        <div className="min-w-full">
          <table
            aria-label="Data table with pagination"
            className="analytics-table w-full bg-white dark:bg-slate-900"
          >
            {headers.length > 0 && (
              <thead>
                <tr>{headers}</tr>
              </thead>
            )}
            <tbody>{paginatedRows}</tbody>
          </table>
        </div>
      </div>

      {/* Pagination Controls */}
      <div className="flex items-center justify-between border-slate-200 border-t bg-slate-50 px-4 py-3 dark:border-slate-700 dark:bg-slate-800">
        <div className="flex items-center gap-2 text-slate-600 text-sm dark:text-slate-400">
          <span>
            Showing {startIndex + 1} to {endIndex} of {rows.length} rows
          </span>
        </div>

        <div className="flex items-center gap-1">
          <Button
            aria-label={t('goToPreviousPage')}
            className="h-8 w-8 p-0"
            disabled={currentPage === 0}
            onClick={handlePrevPage}
            size="sm"
            variant="outline"
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>

          {getVisiblePages().map((page) => (
            <Button
              aria-current={page === currentPage ? 'page' : undefined}
              aria-label={`Go to page ${page + 1}`}
              className="h-8 w-8 p-0"
              key={page}
              onClick={() => handlePageClick(page)}
              size="sm"
              variant={page === currentPage ? 'default' : 'outline'}
            >
              {page + 1}
            </Button>
          ))}

          {totalPages > 6 && currentPage < totalPages - 3 && (
            <>
              <Button
                aria-hidden="true"
                className="h-8 w-8 p-0"
                disabled
                size="sm"
                variant="ghost"
              >
                <MoreHorizontal className="h-4 w-4" />
              </Button>
              <Button
                aria-label={`Go to page ${totalPages}`}
                className="h-8 w-8 p-0"
                onClick={() => handlePageClick(totalPages - 1)}
                size="sm"
                variant="outline"
              >
                {totalPages}
              </Button>
            </>
          )}

          <Button
            aria-label={t('goToNextPage')}
            className="h-8 w-8 p-0"
            disabled={currentPage === totalPages - 1}
            onClick={handleNextPage}
            size="sm"
            variant="outline"
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}

// Virtualized table component for very large datasets
function VirtualizedTableList({ children, className }: VirtualizedTableProps) {
  const t = useTranslations('common');
  const tPagination = useTranslations('pagination');
  const { headers, rows } = extractTableData(children);

  const chunkedRows = useMemo(() => {
    const chunkSize = TABLE_VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE;
    const chunks: React.ReactNode[][] = [];

    for (let i = 0; i < rows.length; i += chunkSize) {
      chunks.push(rows.slice(i, i + chunkSize));
    }

    return chunks;
  }, [rows]);



  return (
    <div className={`w-full ${className || ''}`}>
      <div className="mb-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
        <p className="text-blue-800 text-sm dark:text-blue-200">
          <strong>{t('largeTableDetected')}:</strong>{' '}
          {tPagination('showingRowsInVirtualizedView', {
            count: rows.length.toString(),
          })}
        </p>
      </div>

      <List
        className="thin-scrollbar"
        height={TABLE_VIRTUALIZATION_CONFIG.VIRTUAL_CONTAINER_HEIGHT}
        itemCount={chunkedRows.length}
        itemSize={
          TABLE_VIRTUALIZATION_CONFIG.VIRTUAL_ROW_HEIGHT *
            TABLE_VIRTUALIZATION_CONFIG.ITEMS_PER_PAGE +
          100
        }
        width="100%"
      >
        {({ index, style }) => (
          <TableChunk
            index={index}
            style={style}
            chunkedRows={chunkedRows}
            headers={headers}
            rows={rows}
          />
        )}
      </List>
    </div>
  );
}

// Main virtualized table component with automatic strategy selection
export function VirtualizedTable({
  children,
  enableVirtualization = true,
  className,
  itemsPerPage,
}: VirtualizedTableProps) {
  const { rows } = extractTableData(children);
  const dataSize = rows.length;

  // Small datasets - render normally (handled by CollapsibleTable)
  if (
    !enableVirtualization ||
    dataSize <= TABLE_VIRTUALIZATION_CONFIG.PAGINATION_THRESHOLD
  ) {
    return (
      <div
        className={`thin-scrollbar analytics-table-container w-full max-w-full overflow-x-auto ${className || ''}`}
      >
        <div className="min-w-full">
          <table
            aria-label="Data table"
            className="analytics-table w-full bg-white dark:bg-slate-900"
          >
            {children}
          </table>
        </div>
      </div>
    );
  }

  // Medium datasets - use pagination
  if (dataSize <= TABLE_VIRTUALIZATION_CONFIG.LARGE_TABLE_THRESHOLD) {
    return (
      <PaginatedTable className={className} itemsPerPage={itemsPerPage}>
        {children}
      </PaginatedTable>
    );
  }

  // Large datasets - use virtualization
  return (
    <VirtualizedTableList className={className}>
      {children}
    </VirtualizedTableList>
  );
}

// Utility function to determine if table virtualization is recommended
export function shouldUseTableVirtualization(rowCount: number): {
  recommended: boolean;
  strategy: 'normal' | 'pagination' | 'virtualization';
  reason: string;
} {
  if (rowCount <= TABLE_VIRTUALIZATION_CONFIG.PAGINATION_THRESHOLD) {
    return {
      recommended: false,
      strategy: 'normal',
      reason: 'Table is small enough for normal rendering',
    };
  }

  if (rowCount <= TABLE_VIRTUALIZATION_CONFIG.LARGE_TABLE_THRESHOLD) {
    return {
      recommended: true,
      strategy: 'pagination',
      reason: 'Medium table - pagination recommended for better UX',
    };
  }

  return {
    recommended: true,
    strategy: 'virtualization',
    reason: 'Large table - virtualization required for performance',
  };
}

export default VirtualizedTable;
